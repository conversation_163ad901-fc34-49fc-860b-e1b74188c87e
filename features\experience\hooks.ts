import { API_URLS } from "@/config/api";
import useGetQuery from "@/hooks/useGetQuery";
import { z } from "zod";
import { experienceSchema } from "./model";
import { handleErrors } from "@/lib/errors";
import { ExperienceFilterFields } from "./useExperienceFilterStore";

export const useGetExperiencesQuery = (query?: ExperienceFilterFields) => {
  const res = useGetQuery({
    apiUrl: API_URLS.experiences,
    queryKey: "experiences",
    enabled: true,
    initialData: [],
    query,
  });

  return { ...res, data: z.array(experienceSchema).parse(res.data) };
};

export const useGetExperienceByIdQuery = ({ id }: { id: string }) => {
  const res = useGetQuery({
    apiUrl: API_URLS.experienceById(id),
    queryKey: `experience-${id}`,
    enabled: false,
    initialData: undefined,
  });

  if (!res.data && res.isLoading) return { ...res, data: undefined };

  const schema = experienceSchema.safeParse(res.data);

  !schema.success &&
    handleErrors({
      error: schema.error,
      message: "Erro ao buscar experiência",
    });

  return { ...res, data: schema.data };
};
