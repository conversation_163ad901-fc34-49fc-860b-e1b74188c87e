import { cn } from "@/lib/utils";
import { Ionicons } from "@expo/vector-icons";
import React, { ReactNode, useState } from "react";
import { Modal, Pressable, Text, TouchableOpacity, View } from "react-native";

export const InfoItemText = ({
  title,
  description,
}: {
  title: string;
  description?: string | null;
}) => (
  <Text
    className={cn(
      "text-base text-dark-secondary flex-shrink",
      description && "border-b border-dotted border-primary-1"
    )}
  >
    {title}
  </Text>
);

interface InfoItemProps {
  title: string;
  description?: string | null;
  children: ReactNode;
}

const InfoItem: React.FC<InfoItemProps> = ({
  title,
  description,
  children,
}) => {
  const [isModalVisible, setIsModalVisible] = useState(false);

  const handlePress = () => {
    if (description) {
      setIsModalVisible(true);
    }
  };

  const closeModal = () => {
    setIsModalVisible(false);
  };

  const hasDescription = description && description.trim().length > 0;

  return (
    <>
      <TouchableOpacity
        onPress={handlePress}
        disabled={!hasDescription}
        className="flex-row items-center gap-2"
      >
        {children}
      </TouchableOpacity>

      {/* Modal for displaying description */}
      <Modal
        visible={isModalVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={closeModal}
      >
        <Pressable
          className="flex-1 bg-black/50 justify-center items-center px-4"
          onPress={closeModal}
        >
          <Pressable
            className="bg-white gap-3 rounded-lg p-5 w-full max-w-sm max-h-[60vh] overflow-y-auto"
            onPress={(e) => e.stopPropagation()}
          >
            <View className="flex-row justify-between items-center">
              <Text className="text-xl font-semibold text-dark-primary flex-1 mr-3">
                {title}
              </Text>
              <TouchableOpacity onPress={closeModal} className="p-1">
                <Ionicons
                  name="close"
                  size={24}
                  className="text-dark-primary"
                />
              </TouchableOpacity>
            </View>
            <Text className="text-lg text-tertiary-2 leading-6">
              {description}
            </Text>
          </Pressable>
        </Pressable>
      </Modal>
    </>
  );
};

export default InfoItem;
