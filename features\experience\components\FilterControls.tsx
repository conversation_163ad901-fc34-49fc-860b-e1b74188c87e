import Dropdown, { DropdownItem } from "@/components/Dropdown";
import globalStyles from "@/lib/globalStyles";
import { cn } from "@/lib/utils";
import { MaterialCommunityIcons, Octicons } from "@expo/vector-icons";
import React, { useMemo, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { Pressable, StyleProp, View, ViewStyle } from "react-native";
import {
  ExperienceSortItem,
  experienceSortItems,
} from "../useExperienceFilterStore";

interface FilterControlsProps {
  hasFilters: boolean;
  currentSort?: ExperienceSortItem;
  onClearFilters: () => void;
  onSortChange: (sort: ExperienceSortItem) => void;
  style?: StyleProp<ViewStyle>;
}

const FilterControls = ({
  hasFilters,
  currentSort,
  onClearFilters,
  onSortChange,
  style,
}: FilterControlsProps) => {
  const { t } = useTranslation();
  const [isSortDropdownVisible, setIsSortDropdownVisible] = useState(false);
  const sortDropdownRef = useRef<View>(null);

  // Prepare sort dropdown items
  const sortDropdownItems: DropdownItem<ExperienceSortItem["key"]>[] =
    useMemo(() => {
      return experienceSortItems.map((item) => ({
        id: item.key,
        label: t(`common.${item.key}`),
        value: item.key,
      }));
    }, [t]);

  // Get current sort label
  const getCurrentSortLabel = () => {
    if (!currentSort) {
      return t("common.sort_by");
    }

    const currentSortItem = experienceSortItems.find(
      (item) => item.key === currentSort.key
    );

    return currentSortItem
      ? t(`common.${currentSortItem.key}`)
      : t("common.sort_by");
  };

  // Handle sort selection
  const handleSortSelection = (
    item: DropdownItem<ExperienceSortItem["key"]>
  ) => {
    const sortItem = experienceSortItems.find(
      (sort) => sort.key === item.value
    );
    if (sortItem) {
      onSortChange(sortItem);
    }
    setIsSortDropdownVisible(false);
  };

  // Handle clear filters
  const handleClearFilters = () => {
    onClearFilters();
  };

  return (
    <View
      style={[{ flexDirection: "row", gap: globalStyles.gap["2xs"] }, style]}
    >
      {hasFilters && (
        <Pressable
          onPress={handleClearFilters}
          className={cn("p-1 rounded-full")}
          android_ripple={{
            color: globalStyles.colors.light.primary,
            borderless: true,
          }}
        >
          <MaterialCommunityIcons
            name="filter-remove-outline"
            className="text-primary-1 text-3xl"
          />
        </Pressable>
      )}

      <Pressable
        ref={sortDropdownRef}
        onPress={() => setIsSortDropdownVisible(true)}
        className={cn("p-1 rounded-full")}
        android_ripple={{
          color: globalStyles.colors.light.secondary,
          borderless: true,
        }}
      >
        <Octicons name="sort-desc" className="text-primary-1 text-3xl" />
      </Pressable>

      <Dropdown
        items={sortDropdownItems}
        onSelectItem={handleSortSelection}
        visible={isSortDropdownVisible}
        onClose={() => setIsSortDropdownVisible(false)}
        triggerRef={sortDropdownRef}
        alignment="right"
        containerStyle={{
          minWidth: 150,
        }}
      />
    </View>
  );
};

export default FilterControls;
