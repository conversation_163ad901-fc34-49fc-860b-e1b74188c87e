import AsyncStorage from "@react-native-async-storage/async-storage";
import { AppState, AppStateStatus } from "react-native";

const CACHE_KEYS = {
  names: "names",
  locations: "locations",
} as const;

export interface CacheItem<T> {
  data: T;
  timestamp: number;
  expiresAt: number;
}

export type SearchCacheData = Record<
  keyof typeof CACHE_KEYS,
  { key: string; value: string }[]
>;

class ExperienceSearchCache {
  private static instance: ExperienceSearchCache;
  private readonly CACHE_KEY = "experience_search_cache";
  private readonly CACHE_DURATION = 60 * 60 * 1000; // 1 hour in milliseconds
  private appStateSubscription: any;

  private constructor() {
    this.initializeAppStateListener();
  }

  public static getInstance(): ExperienceSearchCache {
    if (!ExperienceSearchCache.instance) {
      ExperienceSearchCache.instance = new ExperienceSearchCache();
    }
    return ExperienceSearchCache.instance;
  }

  private initializeAppStateListener() {
    this.appStateSubscription = AppState.addEventListener(
      "change",
      this.handleAppStateChange
    );
  }

  private handleAppStateChange = (nextAppState: AppStateStatus) => {
    if (nextAppState === "background" || nextAppState === "inactive") {
      // Clear cache when app goes to background or becomes inactive
      this.clearCache();
    }
  };

  private async getCache(): Promise<CacheItem<SearchCacheData> | null> {
    try {
      const cacheString = await AsyncStorage.getItem(this.CACHE_KEY);
      if (!cacheString) return null;

      const cache: CacheItem<SearchCacheData> = JSON.parse(cacheString);

      // Check if cache has expired
      if (Date.now() > cache.expiresAt) {
        await this.clearCache();
        return null;
      }

      return cache;
    } catch (error) {
      console.error("Error reading cache:", error);
      return null;
    }
  }

  private async setCache(data: SearchCacheData): Promise<void> {
    try {
      const cacheItem: CacheItem<SearchCacheData> = {
        data,
        timestamp: Date.now(),
        expiresAt: Date.now() + this.CACHE_DURATION,
      };

      await AsyncStorage.setItem(this.CACHE_KEY, JSON.stringify(cacheItem));
    } catch (error) {
      console.error("Error setting cache:", error);
    }
  }

  public async clearCache(): Promise<void> {
    try {
      await AsyncStorage.removeItem(this.CACHE_KEY);
    } catch (error) {
      console.error("Error clearing cache:", error);
    }
  }

  public async getCacheData(
    key: keyof SearchCacheData
  ): Promise<SearchCacheData[keyof SearchCacheData] | null> {
    const cache = await this.getCache();
    return cache?.data[key] || null;
  }

  public async setCacheData({
    key,
    value,
  }: {
    key: keyof SearchCacheData;
    value: SearchCacheData[keyof SearchCacheData];
  }): Promise<void> {
    const cache = await this.getCache();
    const currentData = cache?.data || ({} as SearchCacheData);

    await this.setCache({
      ...currentData,
      [key]: value,
    });
  }

  public async isCacheValid(): Promise<boolean> {
    const cache = await this.getCache();
    return cache !== null;
  }

  public async getCacheAge(): Promise<number | null> {
    const cache = await this.getCache();
    if (!cache) return null;

    return Date.now() - cache.timestamp;
  }

  public destroy() {
    if (this.appStateSubscription) {
      this.appStateSubscription.remove();
    }
  }
}

// Export singleton instance
export const experienceSearchCache = ExperienceSearchCache.getInstance();

// Hook for React components
export const useExperienceSearchCache = () => {
  return {
    getCacheData: (key: keyof SearchCacheData) =>
      experienceSearchCache.getCacheData(key),
    setCacheData: (key: keyof SearchCacheData, value: any) =>
      experienceSearchCache.setCacheData({ key, value }),
    clearCache: () => experienceSearchCache.clearCache(),
    isCacheValid: () => experienceSearchCache.isCacheValid(),
    getCacheAge: () => experienceSearchCache.getCacheAge(),
  };
};
