import React, { useState } from "react";
import {
  ActivityIndicator,
  Image,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import { <PERSON><PERSON><PERSON>, Feather, Ionicons } from "@expo/vector-icons";
import { ExperienceType } from "@/features/experience/model";
import useAppAuth from "@/features/auth/hooks/useAppAuth";
import { RenderIf } from "@/components/RenderIf";
import globalStyles from "@/lib/globalStyles";
import Button from "@/components/Button";
import ImageView from "react-native-image-viewing";
import { useTranslation } from "react-i18next";
import { buttonStyles } from "@/components/Button";
import { cn } from "@/lib/utils";

const ExperienceDetailImage = ({
  experience,
  onShare,
  onViewItems,
  hasItems,
}: {
  experience: ExperienceType;
  onShare: () => void;
  onViewItems: () => void;
  hasItems: boolean;
}) => {
  const imgUrl = experience.medias?.[0]?.url;

  const uris =
    experience.medias?.flatMap((media) =>
      media?.url ? [{ uri: media.url }] : []
    ) || [];

  const [showImageView, setShowImageView] = useState(false);
  const [currentImageIndex, setImageIndex] = useState(0);
  const [isFavorite, setIsFavorite] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const { user } = useAppAuth();
  const { t } = useTranslation();

  const handleToggleFavorite = () => {
    setIsLoading(true);
    // TODO: Implement favorite toggle functionality
    setTimeout(() => {
      setIsFavorite(!isFavorite);
      setIsLoading(false);
    }, 500);
  };

  return (
    <View className={cn("flex flex-col items-center gap-8")}>
      <TouchableOpacity
        activeOpacity={0.5}
        onPress={() => setShowImageView(true)}
      >
        <Image
          className={cn(
            "w-full aspect-[4/2.5] shadow-sm rounded-sm border-2 border-light-primary bg-light-primary"
          )}
          source={
            imgUrl
              ? { uri: imgUrl }
              : require("@/assets/images/experience-demo.jpg")
          }
        />
      </TouchableOpacity>
      {imgUrl && (
        <ImageView
          images={uris}
          visible={showImageView}
          imageIndex={currentImageIndex}
          onRequestClose={() => setShowImageView(false)}
          onImageIndexChange={(index) => setImageIndex(index)}
          FooterComponent={({ imageIndex }) => (
            <View className="p-2xs flex flex-row justify-center">
              <Text className="text-white">
                {imageIndex + 1} / {uris.length}
              </Text>
            </View>
          )}
        />
      )}
      <View className={cn("flex flex-row gap-xs justify-center items-center")}>
        <Button
          activeOpacity={0.8}
          size="sm"
          onPress={onShare}
          type="outline"
          style={{ paddingHorizontal: globalStyles.gap.xs }}
        >
          <View
            className={cn("flex flex-row gap-2xs items-center justify-center")}
          >
            <Text style={buttonStyles.outlineText}>
              {t("experience.share")}
            </Text>
            <Entypo
              name="share"
              size={14}
              color={globalStyles.rgba().primary1}
            />
          </View>
        </Button>

        <RenderIf isTrue={!!user}>
          <TouchableOpacity disabled={isLoading} onPress={handleToggleFavorite}>
            <RenderIf isTrue={isLoading}>
              <ActivityIndicator
                size="small"
                color={globalStyles.colors.primary1}
              />
            </RenderIf>
            <RenderIf isTrue={!isLoading}>
              <Ionicons
                name={isFavorite ? "heart-sharp" : "heart-outline"}
                className={cn("text-[25px] text-primary-1")}
              />
            </RenderIf>
          </TouchableOpacity>
        </RenderIf>

        {hasItems && (
          <View className={cn("flex flex-row items-center gap-2xs")}>
            <Button
              activeOpacity={0.8}
              size="sm"
              onPress={onViewItems}
              style={{ paddingHorizontal: globalStyles.gap.xs }}
            >
              <View
                className={cn(
                  "flex flex-row gap-2xs items-center justify-center"
                )}
              >
                <Text style={buttonStyles.primaryText}>
                  {t("experience.view_items")}
                </Text>
                <Feather name="list" className={cn("text-base text-white")} />
              </View>
            </Button>
          </View>
        )}
      </View>
    </View>
  );
};

export default ExperienceDetailImage;
